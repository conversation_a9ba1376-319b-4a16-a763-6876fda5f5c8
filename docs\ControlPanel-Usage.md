# 控制面板组件使用说明

## 概述

`ControlPanel` 组件是青马大桥除湿监测系统的核心控制组件，提供了完整的设备控制和参数设置功能。该组件已成功集成到Dashboard页面中，替换了原有的CameraMonitor组件。

## 功能特性

### 1. 操作模式控制
- **手动模式**: 允许用户手动控制各个设备的启停
- **自动模式**: 系统根据预设参数自动控制设备运行

### 2. 基础控制功能
- **运行状态监控**: 实时显示系统运行状态
- **变频器控制**: 启停变频器和故障复位功能

### 3. 手动控制功能（仅在手动模式下可用）
- 旁路风阀开启/关闭
- 空气预处理器启停
- 除湿机启停
- 进风阀开启/关闭
- 出风口风阀开启/关闭
- 空气预处理器风机启停
- 空气预处理器风速调节（0-100%）

### 4. 参数设置
- **温度设定**: 0-50°C，精度0.1°C
- **湿度设定**: 0-100%，精度0.1%
- **模式设定**: 制冷运行、制热运行、热源制热运行、通风运行、自动
- **开放能需设定**: 0-1000kW

## 技术实现

### API集成
组件使用了专门的API服务 (`src/utils/controlApi.js`) 来处理Modbus设备控制：

```javascript
import {
  setOperationMode,
  setInverterStatus,
  resetInverter,
  setManualControl,
  setFanSpeed,
  setTemperature,
  setHumidity,
  setRunningMode,
  setEnergyDemand,
  getAllControlStatus,
  parseControlStatus
} from '@/utils/controlApi'
```

### 点位映射
组件使用预定义的点位ID映射来控制不同的设备：

```javascript
export const CONTROL_POINT_IDS = {
  OPERATION_MODE: 1,        // 操作模式
  RUNNING_STATUS: 2,        // 运行状态
  INVERTER_STATUS: 3,       // 变频器状态
  INVERTER_RESET: 4,        // 变频器复位
  BYPASS_VALVE: 5,          // 旁路风阀
  AIR_PREPROCESSOR: 6,      // 空气预处理器
  DEHUMIDIFIER: 7,          // 除湿机
  INLET_VALVE: 8,           // 进风阀
  OUTLET_VALVE: 9,          // 出风口风阀
  PREPROCESSOR_FAN: 10,     // 预处理器风机
  FAN_SPEED: 11,            // 风速调节
  TEMPERATURE_SET: 12,      // 温度设定
  HUMIDITY_SET: 13,         // 湿度设定
  MODE_SET: 14,             // 模式设定
  ENERGY_DEMAND_SET: 15     // 开放能需设定
}
```

### 状态管理
组件使用Vue 3的响应式系统来管理状态：

```javascript
// 基础状态
const operationMode = ref(0)      // 0: 手动, 1: 自动
const runningStatus = ref(false)  // 运行状态
const inverterStatus = ref(0)     // 变频器状态

// 手动控制状态
const manualControls = reactive({
  bypassValve: 0,
  airPreprocessor: 0,
  dehumidifier: 0,
  inletValve: 0,
  outletValve: 0,
  preprocessorFan: 0,
  fanSpeed: 50
})

// 参数设置
const parameters = reactive({
  temperature: 25.0,
  humidity: 60.0,
  mode: 6,
  energyDemand: 500
})
```

## 使用方法

### 在Dashboard中使用
组件已经集成到Dashboard页面中：

```vue
<template>
  <div class="left-panels">
    <RealTimeMonitor ... />
    <ControlPanel title="Control Panel" class="monitor-panel" />
    <DeviceStatusMonitor ... />
  </div>
</template>
```

### 独立使用
也可以在其他页面中独立使用：

```vue
<template>
  <div class="control-container">
    <ControlPanel title="设备控制面板" />
  </div>
</template>

<script setup>
import ControlPanel from '@/components/ControlPanel.vue'
</script>
```

## 样式特性

### 设计风格
- 采用蓝色科技风格，与系统整体设计保持一致
- 使用半透明背景和模糊效果
- 响应式设计，支持不同屏幕尺寸

### 主要样式类
- `.control-panel-container`: 主容器
- `.status-section`: 状态显示区域
- `.manual-controls`: 手动控制区域
- `.parameter-settings`: 参数设置区域

### 响应式断点
- 1200px以下: 控制网格变为单列布局
- 768px以下: 优化移动端显示

## 错误处理

### 网络错误
- 自动显示错误消息
- 失败操作会回滚状态
- 提供重试机制

### 状态同步
- 每5秒自动刷新设备状态
- 控制操作后立即刷新相关状态
- 异常情况下的状态恢复

## 权限要求

根据API文档，使用控制面板需要以下权限：
- `qingma:modbusSite:query` - 查询点位状态
- `qingma:modbusSite:cmd` - 执行控制命令

## 配置说明

### 环境变量
确保在 `.env` 文件中配置正确的API地址：

```env
VITE_API_BASE_URL=http://localhost:8080/api/v1
```

### 点位配置
如需修改点位映射，请编辑 `src/utils/controlApi.js` 中的 `CONTROL_POINT_IDS` 配置。

## 故障排除

### 常见问题

1. **控制命令无响应**
   - 检查网络连接
   - 确认API服务正常运行
   - 验证用户权限

2. **状态显示不正确**
   - 检查点位ID配置
   - 确认Modbus设备连接
   - 查看浏览器控制台错误

3. **界面显示异常**
   - 清除浏览器缓存
   - 检查CSS样式冲突
   - 验证Vue组件版本兼容性

### 调试模式
启用调试模式查看详细日志：

```env
VITE_DEBUG=true
```

## 更新日志

### v1.0.0 (2024-08-08)
- 初始版本发布
- 实现基础控制功能
- 集成到Dashboard页面
- 支持手动/自动模式切换
- 完整的参数设置功能

## 后续计划

1. 添加设备状态历史记录
2. 实现控制操作日志
3. 增加批量控制功能
4. 优化移动端体验
5. 添加设备故障诊断功能
