<template>
  <div class="control-panel-container">
    <div class="monitor-header" v-if="title && title.length > 0">
      <h3 class="monitor-title">{{ title }}</h3>
    </div>

    <div class="control-content">
      <!-- 操作模式和运行状态 -->
      <div class="status-section">
        <div class="status-row">
          <div class="status-item">
            <span class="status-label">操作模式</span>
            <el-switch
              v-model="operationMode"
              :active-value="1"
              :inactive-value="0"
              active-text="自动"
              inactive-text="手动"
              @change="handleOperationModeChange"
              :loading="loading.operationMode"
            />
          </div>
          <div class="status-item">
            <span class="status-label">运行状态</span>
            <span :class="['status-badge', runningStatus ? 'running' : 'stopped']">
              {{ runningStatus ? '运行中' : '已停止' }}
            </span>
          </div>
        </div>
        
        <div class="status-row">
          <div class="status-item">
            <span class="status-label">变频器启停</span>
            <el-switch
              v-model="inverterStatus"
              :active-value="1"
              :inactive-value="0"
              active-text="启动"
              inactive-text="停止"
              @change="handleInverterChange"
              :loading="loading.inverter"
            />
          </div>
          <div class="status-item">
            <el-button 
              type="warning" 
              size="small"
              @click="handleInverterReset"
              :loading="loading.inverterReset"
            >
              变频器故障复位
            </el-button>
          </div>
        </div>
      </div>

      <!-- 手动模式控制区域 -->
      <div v-if="operationMode === 0" class="manual-controls">
        <div class="control-group">
          <h4 class="group-title">手动控制</h4>
          <div class="control-grid">
            <div class="control-item">
              <span class="control-label">旁路风阀</span>
              <el-switch
                v-model="manualControls.bypassValve"
                :active-value="1"
                :inactive-value="0"
                active-text="开启"
                inactive-text="关闭"
                @change="handleManualControl('bypassValve', $event)"
              />
            </div>
            <div class="control-item">
              <span class="control-label">空气预处理器</span>
              <el-switch
                v-model="manualControls.airPreprocessor"
                :active-value="1"
                :inactive-value="0"
                active-text="启动"
                inactive-text="停止"
                @change="handleManualControl('airPreprocessor', $event)"
              />
            </div>
            <div class="control-item">
              <span class="control-label">除湿机</span>
              <el-switch
                v-model="manualControls.dehumidifier"
                :active-value="1"
                :inactive-value="0"
                active-text="启动"
                inactive-text="停止"
                @change="handleManualControl('dehumidifier', $event)"
              />
            </div>
            <div class="control-item">
              <span class="control-label">进风阀</span>
              <el-switch
                v-model="manualControls.inletValve"
                :active-value="1"
                :inactive-value="0"
                active-text="开启"
                inactive-text="关闭"
                @change="handleManualControl('inletValve', $event)"
              />
            </div>
            <div class="control-item">
              <span class="control-label">出风口风阀</span>
              <el-switch
                v-model="manualControls.outletValve"
                :active-value="1"
                :inactive-value="0"
                active-text="开启"
                inactive-text="关闭"
                @change="handleManualControl('outletValve', $event)"
              />
            </div>
            <div class="control-item">
              <span class="control-label">预处理器风机</span>
              <el-switch
                v-model="manualControls.preprocessorFan"
                :active-value="1"
                :inactive-value="0"
                active-text="启动"
                inactive-text="停止"
                @change="handleManualControl('preprocessorFan', $event)"
              />
            </div>
          </div>
          
          <!-- 风速调节 -->
          <div class="control-item full-width">
            <span class="control-label">空气预处理器风速调节</span>
            <div class="slider-container">
              <el-slider
                v-model="manualControls.fanSpeed"
                :min="0"
                :max="100"
                :step="1"
                show-input
                @change="handleFanSpeedChange"
              />
              <span class="unit">%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 参数设置区域 -->
      <div class="parameter-settings">
        <h4 class="group-title">参数设置</h4>
        <div class="parameter-grid">
          <div class="parameter-item">
            <span class="parameter-label">温度设定</span>
            <div class="input-with-unit">
              <el-input-number
                v-model="parameters.temperature"
                :min="0"
                :max="50"
                :step="0.1"
                :precision="1"
                size="small"
                @change="handleParameterChange('temperature', $event)"
              />
              <span class="unit">°C</span>
            </div>
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">湿度设定</span>
            <div class="input-with-unit">
              <el-input-number
                v-model="parameters.humidity"
                :min="0"
                :max="100"
                :step="0.1"
                :precision="1"
                size="small"
                @change="handleParameterChange('humidity', $event)"
              />
              <span class="unit">%</span>
            </div>
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">模式设定</span>
            <el-select
              v-model="parameters.mode"
              size="small"
              @change="handleParameterChange('mode', $event)"
            >
              <el-option
                v-for="option in MODE_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
          
          <div class="parameter-item">
            <span class="parameter-label">开放能需设定</span>
            <div class="input-with-unit">
              <el-input-number
                v-model="parameters.energyDemand"
                :min="0"
                :max="1000"
                :step="1"
                size="small"
                @change="handleParameterChange('energyDemand', $event)"
              />
              <span class="unit">kW</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import {
  setOperationMode,
  setInverterStatus,
  resetInverter,
  setManualControl,
  setFanSpeed,
  setTemperature,
  setHumidity,
  setRunningMode,
  setEnergyDemand,
  getAllControlStatus,
  parseControlStatus,
  MODE_OPTIONS
} from '@/utils/controlApi'

const props = defineProps({
  title: {
    type: String,
    default: "Control Panel",
  }
})

// 状态数据
const operationMode = ref(0) // 0: 手动, 1: 自动
const runningStatus = ref(false) // 运行状态
const inverterStatus = ref(0) // 变频器状态

// 加载状态
const loading = reactive({
  operationMode: false,
  inverter: false,
  inverterReset: false
})

// 手动控制状态
const manualControls = reactive({
  bypassValve: 0,
  airPreprocessor: 0,
  dehumidifier: 0,
  inletValve: 0,
  outletValve: 0,
  preprocessorFan: 0,
  fanSpeed: 50
})

// 参数设置
const parameters = reactive({
  temperature: 25.0,
  humidity: 60.0,
  mode: 6,
  energyDemand: 500
})

// 定时器
let statusTimer = null

// 事件处理方法
const handleOperationModeChange = async (value) => {
  loading.operationMode = true
  const success = await setOperationMode(value)
  if (!success) {
    operationMode.value = value ? 0 : 1 // 回滚
  }
  loading.operationMode = false
}

const handleInverterChange = async (value) => {
  loading.inverter = true
  const success = await setInverterStatus(value)
  if (!success) {
    inverterStatus.value = value ? 0 : 1 // 回滚
  }
  loading.inverter = false
}

const handleInverterReset = async () => {
  loading.inverterReset = true
  await resetInverter()
  loading.inverterReset = false
}

const handleManualControl = async (controlType, value) => {
  const success = await setManualControl(controlType, value)
  if (!success) {
    manualControls[controlType] = value ? 0 : 1 // 回滚
  }
}

const handleFanSpeedChange = async (value) => {
  await setFanSpeed(value)
}

const handleParameterChange = async (paramType, value) => {
  switch (paramType) {
    case 'temperature':
      await setTemperature(value)
      break
    case 'humidity':
      await setHumidity(value)
      break
    case 'mode':
      await setRunningMode(value)
      break
    case 'energyDemand':
      await setEnergyDemand(value)
      break
  }
}

// 初始化和状态刷新
const initializeStatus = async () => {
  try {
    const statusData = await getAllControlStatus()
    const parsedStatus = parseControlStatus(statusData)

    // 更新状态
    operationMode.value = parsedStatus.operationMode
    runningStatus.value = parsedStatus.runningStatus
    inverterStatus.value = parsedStatus.inverterStatus

    // 更新手动控制状态
    Object.assign(manualControls, parsedStatus.manualControls)

    // 更新参数设置
    Object.assign(parameters, parsedStatus.parameters)
  } catch (error) {
    console.error('初始化状态失败:', error)
  }
}

const startStatusPolling = () => {
  statusTimer = setInterval(() => {
    initializeStatus()
  }, 5000) // 每5秒刷新一次
}

onMounted(() => {
  initializeStatus()
  startStatusPolling()
})

onUnmounted(() => {
  if (statusTimer) {
    clearInterval(statusTimer)
  }
})
</script>

<style scoped>
.control-panel-container {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitor-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: rgba(0, 162, 255, 0.04);
}

.monitor-title {
  color: #00a2ff;
  font-size: 20px;
  font-weight: 600;
  margin: 8px auto;
}

.control-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 状态区域 */
.status-section {
  background: rgba(0, 162, 255, 0.05);
  border: 1px solid rgba(0, 162, 255, 0.2);
  border-radius: 6px;
  padding: 12px;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.status-row:last-child {
  margin-bottom: 0;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.status-label {
  color: #ffffff;
  font-size: 12px;
  opacity: 0.8;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.running {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  border: 1px solid rgba(0, 255, 136, 0.3);
}

.status-badge.stopped {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

/* 控制组 */
.control-group,
.parameter-settings {
  background: rgba(0, 162, 255, 0.05);
  border: 1px solid rgba(0, 162, 255, 0.2);
  border-radius: 6px;
  padding: 12px;
}

.group-title {
  color: #00a2ff;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
}

.control-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.control-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.control-item.full-width {
  grid-column: 1 / -1;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.control-label {
  color: #ffffff;
  font-size: 11px;
  text-align: center;
  opacity: 0.9;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  max-width: 200px;
}

/* 参数设置 */
.parameter-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.parameter-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.parameter-label {
  color: #ffffff;
  font-size: 12px;
  opacity: 0.9;
}

.input-with-unit {
  display: flex;
  align-items: center;
  gap: 6px;
}

.unit {
  color: #00a2ff;
  font-size: 12px;
  font-weight: 500;
  min-width: 20px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-switch__label) {
  color: #ffffff !important;
  font-size: 11px !important;
}

:deep(.el-switch__label.is-active) {
  color: #00a2ff !important;
}

:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #00a2ff !important;
  border-color: #00a2ff !important;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__wrapper) {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(0, 162, 255, 0.3) !important;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select .el-input__wrapper) {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(0, 162, 255, 0.3) !important;
}

:deep(.el-slider__runway) {
  background-color: rgba(0, 162, 255, 0.2) !important;
}

:deep(.el-slider__bar) {
  background-color: #00a2ff !important;
}

:deep(.el-slider__button) {
  border-color: #00a2ff !important;
  background-color: #00a2ff !important;
}

:deep(.el-button--warning) {
  background: linear-gradient(135deg, #ffaa00 0%, #ff8800 100%);
  border-color: #ffaa00;
  color: #ffffff;
}

:deep(.el-button--warning:hover) {
  background: linear-gradient(135deg, #ffcc33 0%, #ffaa00 100%);
  border-color: #ffcc33;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .control-grid,
  .parameter-grid {
    grid-template-columns: 1fr;
  }

  .status-row {
    flex-direction: column;
    gap: 8px;
  }

  .monitor-title {
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .control-content {
    padding: 8px;
    gap: 12px;
  }

  .control-item.full-width {
    flex-direction: column;
    gap: 8px;
  }

  .slider-container {
    max-width: 100%;
  }
}
</style>
