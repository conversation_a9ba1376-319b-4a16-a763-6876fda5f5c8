/**
 * 控制面板相关的API服务
 * 提供Modbus设备控制和状态查询的接口封装
 */

import { post } from '@/utils/request'
import { ElMessage } from 'element-plus'

// 点位ID映射配置
export const CONTROL_POINT_IDS = {
  // 基础控制点位
  OPERATION_MODE: 1,        // 操作模式：0-手动，1-自动
  RUNNING_STATUS: 2,        // 运行状态：0-停止，1-运行
  INVERTER_STATUS: 3,       // 变频器启停：0-停止，1-启动
  INVERTER_RESET: 4,        // 变频器故障复位
  
  // 手动控制点位
  BYPASS_VALVE: 5,          // 旁路风阀：0-关闭，1-开启
  AIR_PREPROCESSOR: 6,      // 空气预处理器：0-停止，1-启动
  DEHUMIDIFIER: 7,          // 除湿机：0-停止，1-启动
  INLET_VALVE: 8,           // 进风阀：0-关闭，1-开启
  OUTLET_VALVE: 9,          // 出风口风阀：0-关闭，1-开启
  PREPROCESSOR_FAN: 10,     // 空气预处理器风机：0-停止，1-启动
  FAN_SPEED: 11,            // 空气预处理器风速调节：0-100%
  
  // 参数设置点位
  TEMPERATURE_SET: 12,      // 温度设定：0-50°C
  HUMIDITY_SET: 13,         // 湿度设定：0-100%
  MODE_SET: 14,             // 模式设定：1-制冷，2-制热，3-热源制热，4-通风，6-自动
  ENERGY_DEMAND_SET: 15     // 开放能需设定：0-1000kW
}

// 模式设定选项
export const MODE_OPTIONS = [
  { label: '制冷运行', value: 1 },
  { label: '制热运行', value: 2 },
  { label: '热源制热运行', value: 3 },
  { label: '通风运行', value: 4 },
  { label: '自动', value: 6 }
]

/**
 * 执行Modbus控制命令
 * @param {number} pointId - 点位ID
 * @param {number|boolean} value - 控制值
 * @param {string} description - 操作描述
 * @returns {Promise<boolean>} 执行结果
 */
export const executeModbusCommand = async (pointId, value, description) => {
  try {
    const response = await post('/modbusSite/modbusCmd', {
      pointId,
      value,
      cmdType: 'WRITE',
      description
    })
    
    if (response.success) {
      ElMessage.success('控制命令执行成功')
      return true
    } else {
      ElMessage.error(`控制命令执行失败: ${response.message}`)
      return false
    }
  } catch (error) {
    console.error('执行控制命令失败:', error)
    ElMessage.error('网络错误，请检查连接')
    return false
  }
}

/**
 * 批量查询点位状态
 * @param {number[]} pointIds - 点位ID数组
 * @returns {Promise<Array>} 点位状态数据
 */
export const getPointsStatus = async (pointIds) => {
  try {
    const response = await post('/modbusSite/pointStatus', {
      pointIds
    })
    
    if (response.success) {
      return response.data || []
    }
    return []
  } catch (error) {
    console.error('获取点位状态失败:', error)
    return []
  }
}

/**
 * 读取单个点位状态
 * @param {number} pointId - 点位ID
 * @param {string} description - 操作描述
 * @returns {Promise<any>} 点位值
 */
export const readModbusPoint = async (pointId, description) => {
  try {
    const response = await post('/modbusSite/modbusCmd', {
      pointId,
      value: 0, // 读取操作时值可以为任意
      cmdType: 'READ',
      description
    })
    
    if (response.success) {
      return response.data
    }
    return null
  } catch (error) {
    console.error('读取点位失败:', error)
    return null
  }
}

// 控制面板专用API方法

/**
 * 切换操作模式
 * @param {number} mode - 0: 手动, 1: 自动
 * @returns {Promise<boolean>}
 */
export const setOperationMode = async (mode) => {
  return await executeModbusCommand(
    CONTROL_POINT_IDS.OPERATION_MODE,
    mode,
    `切换到${mode ? '自动' : '手动'}模式`
  )
}

/**
 * 控制变频器启停
 * @param {number} status - 0: 停止, 1: 启动
 * @returns {Promise<boolean>}
 */
export const setInverterStatus = async (status) => {
  return await executeModbusCommand(
    CONTROL_POINT_IDS.INVERTER_STATUS,
    status,
    `${status ? '启动' : '停止'}变频器`
  )
}

/**
 * 变频器故障复位
 * @returns {Promise<boolean>}
 */
export const resetInverter = async () => {
  return await executeModbusCommand(
    CONTROL_POINT_IDS.INVERTER_RESET,
    1,
    '变频器故障复位'
  )
}

/**
 * 手动控制设备
 * @param {string} deviceType - 设备类型
 * @param {number} value - 控制值
 * @returns {Promise<boolean>}
 */
export const setManualControl = async (deviceType, value) => {
  const deviceMap = {
    bypassValve: { pointId: CONTROL_POINT_IDS.BYPASS_VALVE, name: '旁路风阀' },
    airPreprocessor: { pointId: CONTROL_POINT_IDS.AIR_PREPROCESSOR, name: '空气预处理器' },
    dehumidifier: { pointId: CONTROL_POINT_IDS.DEHUMIDIFIER, name: '除湿机' },
    inletValve: { pointId: CONTROL_POINT_IDS.INLET_VALVE, name: '进风阀' },
    outletValve: { pointId: CONTROL_POINT_IDS.OUTLET_VALVE, name: '出风口风阀' },
    preprocessorFan: { pointId: CONTROL_POINT_IDS.PREPROCESSOR_FAN, name: '预处理器风机' }
  }
  
  const device = deviceMap[deviceType]
  if (!device) {
    ElMessage.error('未知的设备类型')
    return false
  }
  
  return await executeModbusCommand(
    device.pointId,
    value,
    `手动控制${device.name}: ${value ? '开启' : '关闭'}`
  )
}

/**
 * 设置风速
 * @param {number} speed - 风速百分比 (0-100)
 * @returns {Promise<boolean>}
 */
export const setFanSpeed = async (speed) => {
  return await executeModbusCommand(
    CONTROL_POINT_IDS.FAN_SPEED,
    speed,
    `设置风速为${speed}%`
  )
}

/**
 * 设置温度
 * @param {number} temperature - 温度值 (°C)
 * @returns {Promise<boolean>}
 */
export const setTemperature = async (temperature) => {
  return await executeModbusCommand(
    CONTROL_POINT_IDS.TEMPERATURE_SET,
    temperature,
    `设置温度为${temperature}°C`
  )
}

/**
 * 设置湿度
 * @param {number} humidity - 湿度值 (%)
 * @returns {Promise<boolean>}
 */
export const setHumidity = async (humidity) => {
  return await executeModbusCommand(
    CONTROL_POINT_IDS.HUMIDITY_SET,
    humidity,
    `设置湿度为${humidity}%`
  )
}

/**
 * 设置运行模式
 * @param {number} mode - 运行模式
 * @returns {Promise<boolean>}
 */
export const setRunningMode = async (mode) => {
  const modeText = MODE_OPTIONS.find(option => option.value === mode)?.label || '未知模式'
  return await executeModbusCommand(
    CONTROL_POINT_IDS.MODE_SET,
    mode,
    `设置运行模式为${modeText}`
  )
}

/**
 * 设置开放能需
 * @param {number} energyDemand - 能需值 (kW)
 * @returns {Promise<boolean>}
 */
export const setEnergyDemand = async (energyDemand) => {
  return await executeModbusCommand(
    CONTROL_POINT_IDS.ENERGY_DEMAND_SET,
    energyDemand,
    `设置开放能需为${energyDemand}kW`
  )
}

/**
 * 获取所有控制点位的状态
 * @returns {Promise<Array>}
 */
export const getAllControlStatus = async () => {
  const pointIds = Object.values(CONTROL_POINT_IDS)
  return await getPointsStatus(pointIds)
}

/**
 * 解析点位状态数据
 * @param {Array} statusData - 点位状态数据
 * @returns {Object} 解析后的状态对象
 */
export const parseControlStatus = (statusData) => {
  const status = {
    operationMode: 0,
    runningStatus: false,
    inverterStatus: 0,
    manualControls: {
      bypassValve: 0,
      airPreprocessor: 0,
      dehumidifier: 0,
      inletValve: 0,
      outletValve: 0,
      preprocessorFan: 0,
      fanSpeed: 50
    },
    parameters: {
      temperature: 25.0,
      humidity: 60.0,
      mode: 6,
      energyDemand: 500
    }
  }
  
  statusData.forEach(point => {
    if (point.success) {
      switch (point.pointId) {
        case CONTROL_POINT_IDS.OPERATION_MODE:
          status.operationMode = point.value
          break
        case CONTROL_POINT_IDS.RUNNING_STATUS:
          status.runningStatus = point.value === 1
          break
        case CONTROL_POINT_IDS.INVERTER_STATUS:
          status.inverterStatus = point.value
          break
        case CONTROL_POINT_IDS.BYPASS_VALVE:
          status.manualControls.bypassValve = point.value
          break
        case CONTROL_POINT_IDS.AIR_PREPROCESSOR:
          status.manualControls.airPreprocessor = point.value
          break
        case CONTROL_POINT_IDS.DEHUMIDIFIER:
          status.manualControls.dehumidifier = point.value
          break
        case CONTROL_POINT_IDS.INLET_VALVE:
          status.manualControls.inletValve = point.value
          break
        case CONTROL_POINT_IDS.OUTLET_VALVE:
          status.manualControls.outletValve = point.value
          break
        case CONTROL_POINT_IDS.PREPROCESSOR_FAN:
          status.manualControls.preprocessorFan = point.value
          break
        case CONTROL_POINT_IDS.FAN_SPEED:
          status.manualControls.fanSpeed = point.value
          break
        case CONTROL_POINT_IDS.TEMPERATURE_SET:
          status.parameters.temperature = point.value
          break
        case CONTROL_POINT_IDS.HUMIDITY_SET:
          status.parameters.humidity = point.value
          break
        case CONTROL_POINT_IDS.MODE_SET:
          status.parameters.mode = point.value
          break
        case CONTROL_POINT_IDS.ENERGY_DEMAND_SET:
          status.parameters.energyDemand = point.value
          break
      }
    }
  })
  
  return status
}

export default {
  CONTROL_POINT_IDS,
  MODE_OPTIONS,
  executeModbusCommand,
  getPointsStatus,
  readModbusPoint,
  setOperationMode,
  setInverterStatus,
  resetInverter,
  setManualControl,
  setFanSpeed,
  setTemperature,
  setHumidity,
  setRunningMode,
  setEnergyDemand,
  getAllControlStatus,
  parseControlStatus
}
